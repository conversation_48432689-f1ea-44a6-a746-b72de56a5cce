buildscript {
    apply from: 'dependencies.gradle'
}

plugins {
    id "org.jetbrains.kotlin.jvm" version "${kotlinVersion}"
    id "org.jetbrains.kotlin.plugin.allopen" version "${kotlinVersion}"
    id "org.jetbrains.kotlin.plugin.serialization" version "${kotlinVersion}"
    id "com.github.johnrengelman.shadow" version "8.1.1"
    id "com.google.devtools.ksp" version "2.1.21-2.0.2"
    id "application"
}

// transform current date & time into release version, f.e.
// 10/25[18.15.40]  => 1025.b181540
static def getImplementationVersion() {
    return new java.text.SimpleDateFormat("MM/dd[HH.mm.ss]")
            .format(new Date())
            .replace(".", "")
            .replace("/", "")
            .replace("[", ".b")
            .replace("]", "")
}

def buildVersion = getImplementationVersion()

version "$buildVersion"
group "trading"

repositories {
    mavenCentral()
    maven {
        name = "GitHubPackages"
        url "https://maven.pkg.github.com/arbatora/booker-client"
        credentials {
            username = project.findProperty("gpr.user") ?: System.getenv("GITHUB_ACTOR")
            password = project.findProperty("gpr.key") ?: System.getenv("GITHUB_TOKEN")
        }
    }
}

configurations {
    // for dependencies that are needed for development only
    developmentOnly
}

dependencies {
    compileOnly(platform("io.micronaut:micronaut-core-bom:$micronautVersion"))
    implementation(platform("io.micronaut:micronaut-core-bom:$micronautVersion"))
    implementation("io.micronaut:micronaut-inject")
    implementation("org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion")
    implementation("org.jetbrains.kotlin:kotlin-reflect:$kotlinVersion")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinCoroutinesVersion")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$kotlinCoroutinesVersion")
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime:$micronautKotlinVersion")
    implementation("io.micronaut:micronaut-runtime")
    implementation("io.micronaut:micronaut-http-server-netty")
    implementation("io.micronaut:micronaut-http-client")
    implementation('io.micronaut:micronaut-management')

    ksp("io.micronaut:micronaut-inject-kotlin:$micronautVersion")
    kspTest("io.micronaut:micronaut-inject-kotlin:$micronautVersion")

    // additional
    runtimeOnly("org.yaml:snakeyaml")
    implementation("io.micronaut:micronaut-jackson-databind")
    implementation('io.micronaut.redis:micronaut-redis-lettuce:6.8.0')
    implementation('com.squareup.okhttp3:okhttp:4.12.0')
    implementation('io.lettuce:lettuce-core:6.7.1.RELEASE')
    implementation('org.apache.commons:commons-compress:1.27.1')
    implementation("com.github.kittinunf.fuel:fuel:$fuelVersion")
    implementation("com.github.kittinunf.fuel:fuel-jackson:$fuelVersion")
    implementation("com.github.kittinunf.fuel:fuel-coroutines:$fuelVersion")
    implementation('net.jodah:expiringmap:0.5.11')
    // --

    // our own
    implementation("trading.booker:client:6.0.2")
    implementation("trading.arb:metrics:5.0.0")
    // --

    // aws
    implementation(platform("software.amazon.awssdk:bom:2.31.68"))
    implementation("software.amazon.awssdk:regions")
    implementation("software.amazon.awssdk:cloudwatch")
    // --

    // json
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.19.1")
    implementation('org.jetbrains.kotlinx:kotlinx-serialization-json:1.8.1')
    // --

    // web3
    implementation("org.web3j:core:4.12.1")
    implementation("org.msgpack:msgpack-core:0.9.9")
    // --

    developmentOnly("io.micronaut:micronaut-runtime-osx:$micronautVersion")
    implementation('ch.qos.logback:logback-classic:1.5.18')

    testImplementation(enforcedPlatform("io.micronaut:micronaut-core-bom:$micronautVersion"))

    // additional test
    testImplementation('org.awaitility:awaitility-kotlin:4.3.0')
    testImplementation('org.objenesis:objenesis:3.4')
    testImplementation('net.bytebuddy:byte-buddy:1.17.6')
    testImplementation('com.github.codemonstur:embedded-redis:1.4.3')
    // --

    // kotest
    testImplementation("io.kotest:kotest-assertions-core:5.9.1")
    testImplementation("io.kotest:kotest-runner-junit5:5.9.1")
    testImplementation("io.kotest:kotest-framework-datatest:5.9.1")
    testImplementation("io.mockk:mockk:1.14.4")
}

test.classpath += configurations.developmentOnly

mainClassName = "trading.Application"

// use JUnit 5 platform
test {
    useJUnitPlatform()
}

java {
    sourceCompatibility = JavaVersion.VERSION_23
    targetCompatibility = JavaVersion.VERSION_23
}

allOpen {
    annotation("io.micronaut.aop.Around")
}

kotlin {
    jvmToolchain(23)
}

ksp {
    arg("micronaut.processing.group", "trading")
    arg("micronaut.processing.module", "trading")
    arg("micronaut.processing.annotations", "trading.*")
}

jar {
    manifest {
        attributes 'Implementation-Version': "$buildVersion"
    }
}

shadowJar {
    mergeServiceFiles()
}

tasks.withType(JavaExec) {
    classpath += configurations.developmentOnly
    jvmArgs('-XX:TieredStopAtLevel=1', '-Dcom.sun.management.jmxremote')
    if (gradle.startParameter.continuous) {
        systemProperties(
                'micronaut.io.watch.restart':'true',
                'micronaut.io.watch.enabled':'true',
                "micronaut.io.watch.paths":"src/main"
        )
    }
}
