micronaut:
  application:
    name: trading

  server:
    port: 8008

  executors:
    scheduled:
      type: scheduled
      core-pool-size: 16

logging:
  response: false

arbpath: /tmp/tmp-arb

redis:
  uri: redis://localhost

# You can override booker port to connect to using the configuration below.
#booker:
#  client:
#    port: 7755

# Each exchange top property has an implicit property `booker=true`. When
# property set as `false` bot bypasses booker and reach an exchange directly.
# Look at trading/trading/holders/OrderBookHolder.kt:167
bitmex:
  api:
    version: v1
  keys:
    socket:
      id: GZVQ43GHDzZWggBdGMTVXWvv
      secret: A2HnlDJEF9zIMF-EsIlo-D_hu3Mqds6PUdwOV3T2tdcLvNKa
    rest:
      id: GZVQ43GHDzZWggBdGMTVXWvv
      secret: A2HnlDJEF9zIMF-EsIlo-D_hu3Mqds6PUdwOV3T2tdcLvNKa
  host: testnet.bitmex.com
  socket:
    host: wss://ws.testnet.bitmex.com
    subscribers: 1
    proxy: false
  proxy: false

deribit:
  keys:
    all:
      id: DLQM2TaPSwaa
      secret: CRTCQAM76VMMO6ZQTJ2JH7JWKJZWHEOK
  rest:
    address: https://test.deribit.com
  socket:
    address: wss://test.deribit.com
    subscribers: 2
    proxy: false
  proxy: false
  wsapi: true

huobi:
  keys:
    all:
      id:
      secret:
  host: api.hbdm.vn

okex:
  keys:
    v5:
      id:
      secret:
      passphrase:
  socket:
    address: wss://ws.okx.com:8443/ws/v5
    subscribers: 2
    proxy: false
  address: https://www.okx.com
  proxyaddress: https://coloapi2.okx.com
  proxy: false
  wsapi: false

facilities:
  keys:
    v3:
      id:
      secret:
  host: www.cryptofacilities.com
  constants:
    XBT:
      step: 0.5
      min: 0
    ETH:
      step: 0.05
      min: 0

binance:
  keys:
    v1:
      id: 831b7b589dcc5f29f713dcbdcd7e96081976c9b0d4568cd0fbd4339c2bc79444
      secret: 605bb78de94d175ef371724f3759e4e70377c6f4a8fd14c3a23c081ce3f35e77
  host: https://testnet.binancefuture.com # https://fapi.binance.com/fapi/v1/exchangeInfo
  proxy: false # usdt rest proxy
  socket:
    subscribers: 1
    host: wss://stream.binancefuture.com # fstream.binance.com
    proxy: false # usdt socket proxy
    api:
      host: wss://testnet.binancefuture.com # wss://ws-fapi.binance.com/ws-fapi/v1
      proxy: false
  coin:
    host: https://testnet.binancefuture.com # https://dapi.binance.com/dapi/v1/exchangeInfo
    proxy: false # coin rest proxy
    socket:
      subscribers: 1
      host: wss://dstream.binancefuture.com # dstream.binance.com
      proxy: false # coin socket proxy
  wsapi: false

bybit:
  keys:
    v5:
      id: 37nnED8Egjf8GviahG
      secret: KJHXmxVlqmHpZovLTaTrgryViu1Hr7PcdZxu
  host: api-testnet.bybit.com
  socket:
    host: wss://stream-testnet.bybit.com
    subscribers: 1
    proxy: false
  proxy: false
  wsapi: false

hyperliquid:
  wallet:
  key:
  address: https://api2.hyperliquid.xyz
  socket:
    # First host within hosts array is a primary one for sending limit orders
    hosts: ["wss://api2.hyperliquid.xyz", "wss://api.hyperliquid.xyz"]
    subscribers: 2
    proxy: false
  # If true then turns on making limit orders over websockets
  wsapi: false
  nodes:
    - host: ws://***********:8822
      channels: trades
    - host: ws://***********:8822
      channels: trades, orders
  maintenance: https://hyperliquid.statuspage.io

bitget:
  keys:
    id:
    secret:
    passphrase:
  address: https://api.bitget.com
  socket:
    address: wss://ws.bitget.com/v2/ws
    subscribers: 2

gateio:
  keys:
    id:
    secret:
  address: https://api.gateio.ws
  socket:
    address: wss://fx-ws.gateio.ws/v4/ws/usdt
    subscribers: 2

telegram:
  key: *********************************************
  id: 196085927
