# Get the current branch name
BR<PERSON><PERSON> ?= $(shell git rev-parse --abbrev-ref HEAD)

# Creates a sandbox branch from the passed branch of the current one.
.PHONY: .sandbox
.sandbox:
	@echo "Using source branch: $(BRANCH)"
	git branch -D $(SANDBOX) 2>/dev/null || true
	git push origin :$(SANDBOX) 2>/dev/null || true
	git stash push -m "Changes saved before making $(SANDBOX)"
	git checkout release
	git pull
	git checkout -b $(SANDBOX)
	git merge --ff --no-edit $(BRANCH)
	git push origin -u $(SANDBOX):$(SANDBOX)
	git checkout $(BRANCH)
	@STASH_REF=$$(git stash list | head -1 | grep "Changes saved before making" | grep -o "stash@{[0-9]*}"); \
	if [ -n "$$STASH_REF" ]; then \
		git stash pop $$STASH_REF; \
	fi

.PHONY: ai-sandbox
ai-sandbox:
	make .sandbox SANDBOX=ai-sandbox

.PHONY: ks-sandbox
ks-sandbox:
	make .sandbox SANDBOX=ks-sandbox

.PHONY: test
test:
	./gradlew test -i

.PHONY: run
run:
	./gradlew run
